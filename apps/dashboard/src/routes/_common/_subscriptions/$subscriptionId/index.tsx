import { use$ } from '@legendapp/state/react'
import { global$, subscriptionsApi } from '@mass/api'
import { subscription$ } from '@mass/components/dashboard'
import { Badge, Text, Title } from '@mass/components/shared'
import { useDate } from '@mass/utils'
import { createFileRoute } from '@tanstack/react-router'
import clsx from 'clsx'
import { useTranslation } from 'react-i18next'

function Subscription() {
  const { t: common } = useTranslation('common')
  const subscription = use$(() => subscription$.selectedSubscription.get())
  const format = useDate('DD MMMM YYYY')

  const distributionCompany = use$(() =>
    global$.subscription.regions.get()?.find(region => region.id === subscription?.regionId),
  )

  const subscriptionInfosEntries = [
    {
      label: common('subscription-name'),
      value: subscription?.name,
    },
    {
      label: common('distribution-company'),
      value: distributionCompany?.name,
    },
    {
      label: common('installation-id'),
      value: subscription?.installationId,
    },
    {
      label: common('facility-type'),
      el: (
        <Badge mode='success' withDot className='w-max'>
          {subscription?.type === 'electricity-production' ? common('production') : common('consumption')}
        </Badge>
      ),
    },
    {
      label: common('address'),
      value: subscription?.details.address,
    },
  ]

  const consumerDataEntries = [
    {
      label: common('subscription-type'),
      value: subscription?.individual ? common('neutral-person') : common('artificial-person'),
    },
    {
      label: subscription?.individual ? common('person-identifier-real') : common('person-identifier'),
      value: subscription?.personIdentifier,
    },
    {
      label: common('subscription-date'),
      value: format(subscription?.startDate ?? ''),
    },
  ]

  return (
    <div
      className={clsx(
        'flex flex-col', // flex
        'gap-8 p-8 md:gap-10 md:p-16', // spacing
      )}>
      <div
        className={clsx(
          'flex flex-col', // flex
          'rounded-b2 border border-accessory-1', // border
          'relative w-full overflow-hidden',
        )}>
        <div
          className={clsx(
            'flex items-center',
            'px-10 py-8', // spacing
            'bg-primary-light', // styling
            'border-accessory-1 border-b',
          )}>
          <Title variant='primary'> {common('subscription-informations')} </Title>
        </div>
        {subscriptionInfosEntries.map((entry, index) => (
          <div
            key={entry.label}
            className={clsx(
              'flex flex-col justify-center',
              'gap-2 px-10 py-6', // spacing
              {
                'border-accessory-1 border-b': index !== subscriptionInfosEntries.length - 1,
              },
            )}>
            <Title variant='h5' el='h2'>
              {entry.label}
            </Title>
            {entry.value && <Text variant='dim-2'>{entry.value}</Text>}
            {entry.el ?? null}
          </div>
        ))}
      </div>

      <div
        className={clsx(
          'flex flex-col', // flex
          'rounded-b2 border border-accessory-1', // border
          'relative w-full overflow-hidden',
        )}>
        <div
          className={clsx(
            'flex items-center',
            'px-10 py-8', // spacing
            'bg-primary-light', // styling
            'border-accessory-1 border-b',
          )}>
          <Title variant='primary'> {common('consumer-data')} </Title>
        </div>
        {consumerDataEntries.map((entry, index) => (
          <div
            key={entry.label}
            className={clsx(
              'flex flex-col justify-center',
              'gap-2 px-10 py-6', // spacing
              {
                'border-accessory-1 border-b': index !== subscriptionInfosEntries.length - 1,
              },
            )}>
            <Title variant='h5' el='h2'>
              {entry.label}
            </Title>
            {entry.value && <Text variant='dim-2'>{entry.value}</Text>}
          </div>
        ))}
      </div>
    </div>
  )
}

export const Route = createFileRoute('/_common/_subscriptions/$subscriptionId/')({
  async beforeLoad({ params }) {
    const subscriptionId = params.subscriptionId

    const subscription = await subscriptionsApi.detail({
      query: {
        id: subscriptionId,
      },
    })

    subscription$.selectedSubscription.set(subscription)
  },

  component: Subscription,
})
