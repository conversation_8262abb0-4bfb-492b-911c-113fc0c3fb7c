import { subscriptionsApi } from '@mass/api'
import { subscription$ } from '@mass/components/dashboard'
import { Button, Checkbox, DatePicker, Radio, RadioGroup, Text, Title } from '@mass/components/shared'
import type { BlobType } from '@mass/utils'
import { createFileRoute } from '@tanstack/react-router'
import clsx from 'clsx'
import { useState } from 'react'
import { useTranslation } from 'react-i18next'

const LAST_X_PERIODS = {
  daily: 10,
  monthly: 6,
  yearly: null,
}

function SubscriptionQuery() {
  const { t: common } = useTranslation('common')
  const { t: dashboard } = useTranslation('dashboard')

  const [selectedPeriod, setSelectedPeriod] = useState<keyof typeof LAST_X_PERIODS>('yearly')
  const [isLastX, setIsLastX] = useState(false)
  const [isRange, setIsRange] = useState(false)
  const [startDate, setStartDate] = useState<string | null>(null)

  return (
    <div
      className={clsx(
        'flex flex-col', // flex
        'gap-8 p-8 md:gap-16 md:p-16', // spacing
      )}>
      <div
        className={clsx(
          'flex flex-col justify-between md:flex-row', // flex
          'gap-8 pb-8 md:pb-16', // spacing
          'border-accessory-1 border-b', // border
          'relative w-full overflow-hidden',
        )}>
        <div className='flex flex-col gap-2'>
          <Title el='h1' variant='h4'>
            {dashboard('subscriptions.query-params')}
          </Title>

          <Text variant='dim-2'> {dashboard('subscriptions.query-params-description')} </Text>
        </div>

        <div className='flex flex-row items-center gap-4'>
          <Button variant='bordered' className='rounded-c1'>
            {common('clear')}
          </Button>

          <Button variant='primary' className='rounded-c1'>
            {common('apply')}
          </Button>
        </div>
      </div>

      <div
        className={clsx(
          'grid grid-cols-1 md:grid-cols-2', // grid
          'gap-8 md:gap-10', // spacing
        )}>
        <div
          className={clsx(
            'flex flex-col', // flex
            'gap-12 md:gap-18', // spacing
          )}>
          <div className='flex flex-col gap-4 md:gap-6'>
            <Title el='h3' variant='h5'>
              {common('period-type')}
            </Title>

            <RadioGroup
              name='test-options'
              value={selectedPeriod}
              onChange={setSelectedPeriod as BlobType}
              error={false}
              disabled={false}>
              <Radio value='daily' label={common('daily')} />
              <Radio value='monthly' label={common('monthly')} />
              <Radio value='yearly' label={common('yearly')} />
            </RadioGroup>
          </div>

          <div className='flex flex-col gap-4 md:gap-6'>
            <Title el='h3' variant='h5'>
              {common('date')}
            </Title>

            <DatePicker placeholder={common('start-date')} value={startDate} onChange={value => setStartDate(value)} />

            {selectedPeriod !== 'yearly' && (
              <Checkbox
                label={common('date-range')}
                checked={isRange}
                onChange={e => {
                  setIsLastX(false)
                  setIsRange(e.target.checked)
                }}
              />
            )}
          </div>
        </div>

        <div
          className={clsx(
            'flex flex-col', // flex
            'gap-12 md:gap-18', // spacing
          )}>
          {LAST_X_PERIODS[selectedPeriod] && (
            <div className='flex flex-col gap-4 md:gap-6'>
              <Title el='h3' variant='h5'>
                {common('analyze-type')}
              </Title>

              <Checkbox
                label={dashboard('subscriptions.last-x', {
                  period: common(selectedPeriod),
                  count: LAST_X_PERIODS[selectedPeriod],
                })}
                checked={isLastX}
                onChange={e => setIsLastX(e.target.checked)}
                disabled={isRange}
              />
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

export const Route = createFileRoute('/_common/_subscriptions/$subscriptionId/query')({
  async beforeLoad({ params }) {
    const subscriptionId = params.subscriptionId

    const subscription = await subscriptionsApi.detail({
      query: {
        id: subscriptionId,
      },
    })

    subscription$.selectedSubscription.set(subscription)
  },

  component: SubscriptionQuery,
})
