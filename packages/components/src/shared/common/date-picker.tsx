import 'react-day-picker/style.css'

import { CalendarIcon } from '@mass/icons'
import { useDate } from '@mass/utils'
import clsx from 'clsx'
import { type FC, useId } from 'react'
import { DayPicker } from 'react-day-picker'
import { Input, type InputStylesProps } from './input'
import { Popover } from './popover'

export interface DatePickerProps extends Omit<InputStylesProps, 'variant'> {
  value?: Date | string | null
  onChange?: (date: Date | undefined) => void
  placeholder?: string
  disabled?: boolean
  readOnly?: boolean
  fromDate?: Date
  toDate?: Date
  disabledDays?: Date[]
  className?: string
  inputClassName?: string
  calendarClassName?: string
}

export const DatePicker: FC<DatePickerProps> = ({
  value,
  onChange,
  placeholder,
  disabled = false,
  readOnly = false,
  fromDate,
  toDate,
  disabledDays,
  className,
  inputClassName,
  calendarClassName,
  error = false,
  ...props
}) => {
  const id = useId()
  const formatDate = useDate('DD/MM/YYYY')

  // Convert value to Date if it's a string
  const dateValue = value ? (typeof value === 'string' ? new Date(value) : value) : undefined

  // Format date for display
  const displayValue = dateValue ? formatDate(dateValue) : ''

  const handleDateSelect = (date: Date | undefined) => {
    onChange?.(date)
  }

  const handleInputClick = () => {
    // This will be handled by the Popover component
  }

  const handleInputKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' || e.key === ' ') {
      e.preventDefault()
      handleInputClick()
    }
  }

  return (
    <div className={clsx('relative w-full', className)}>
      <Popover
        variant='bordered'
        className='w-full'
        popoverUnstyled
        popoverPosition='bottom'
        buttonContent={({ open }) => (
          <div className='relative w-full'>
            <Input
              id={id}
              type='text'
              value={displayValue}
              placeholder={placeholder}
              readOnly
              disabled={disabled}
              error={error}
              className={clsx(
                'cursor-pointer pr-12', // space for icon
                inputClassName,
              )}
              onClick={handleInputClick}
              onKeyDown={handleInputKeyDown}
              {...props}
            />
            <CalendarIcon
              className={clsx(
                '-translate-y-1/2 pointer-events-none absolute top-1/2 right-3 h-5 w-5 transform', // positioning
                'text-dim-3', // color
                {
                  'text-dim-3': disabled,
                  'text-primary': open && !disabled,
                },
              )}
            />
          </div>
        )}>
        <div className={clsx('rounded-c1 border border-accessory-1 bg-white p-4 shadow-layer-1', calendarClassName)}>
          <DayPicker
            mode='single'
            selected={dateValue}
            onSelect={handleDateSelect}
            {...(fromDate && { startMonth: fromDate })}
            {...(toDate && { endMonth: toDate })}
            {...(disabledDays && { disabled: disabledDays })}
            className={clsx(
              'rdp', // react-day-picker base class
              '[&_.rdp-months]:flex [&_.rdp-months]:flex-col [&_.rdp-months]:space-y-4',
              '[&_.rdp-month]:space-y-4',
              '[&_.rdp-caption]:relative [&_.rdp-caption]:flex [&_.rdp-caption]:items-center [&_.rdp-caption]:justify-center [&_.rdp-caption]:pt-1',
              '[&_.rdp-caption_label]:font-medium [&_.rdp-caption_label]:text-sm',
              '[&_.rdp-nav]:flex [&_.rdp-nav]:items-center [&_.rdp-nav]:space-x-1',
              '[&_.rdp-nav_button]:h-7 [&_.rdp-nav_button]:w-7 [&_.rdp-nav_button]:bg-transparent [&_.rdp-nav_button]:p-0 [&_.rdp-nav_button]:opacity-50 [&_.rdp-nav_button]:hover:opacity-100',
              '[&_.rdp-table]:w-full [&_.rdp-table]:border-collapse [&_.rdp-table]:space-y-1',
              '[&_.rdp-head_row]:flex',
              '[&_.rdp-head_cell]:w-8 [&_.rdp-head_cell]:rounded-md [&_.rdp-head_cell]:font-normal [&_.rdp-head_cell]:text-[0.8rem] [&_.rdp-head_cell]:text-dim-3',
              '[&_.rdp-row]:mt-2 [&_.rdp-row]:flex [&_.rdp-row]:w-full',
              '[&_.rdp-cell]:relative [&_.rdp-cell]:p-0 [&_.rdp-cell]:text-center [&_.rdp-cell]:text-sm [&_.rdp-cell]:focus-within:relative [&_.rdp-cell]:focus-within:z-20',
              '[&_.rdp-button]:h-8 [&_.rdp-button]:w-8 [&_.rdp-button]:rounded-md [&_.rdp-button]:p-0 [&_.rdp-button]:font-normal [&_.rdp-button]:hover:bg-black/5 [&_.rdp-button]:focus:bg-black/5',
              '[&_.rdp-day_today]:bg-accessory-1 [&_.rdp-day_today]:text-black',
              '[&_.rdp-day_selected]:bg-primary [&_.rdp-day_selected]:text-white [&_.rdp-day_selected]:hover:bg-primary [&_.rdp-day_selected]:focus:bg-primary',
              '[&_.rdp-day_disabled]:text-dim-3 [&_.rdp-day_disabled]:opacity-50',
              '[&_.rdp-day_outside]:text-dim-3 [&_.rdp-day_outside]:opacity-50',
            )}
          />
        </div>
      </Popover>
    </div>
  )
}
